// 地图系统模块
export class MapSystem {
    constructor(gameState, soundManager) {
        this.gameState = gameState;
        this.soundManager = soundManager;
        this.worldMap = this.generateWorldMap();
        this.currentViewLevel = 'street'; // 当前查看的层级
        this.currentViewPath = []; // 当前查看路径
    }

    // 生成完整的世界地图结构
    generateWorldMap() {
        const world = {
            name: '废土联邦',
            type: 'country',
            explored: false,
            provinces: {}
        };

        // 生成省份
        const provinceNames = ['华东省', '华南省', '华北省', '西南省', '东北省', '西北省', '远东省', '西南边省', '东南省', '北方省', '南方省'];
        for (let i = 0; i < provinceNames.length; i++) {
            const provinceId = `province_${i}`;
            world.provinces[provinceId] = this.generateProvince(provinceNames[i], provinceId);
        }

        return world;
    }

    // 生成省份
    generateProvince(name, id) {
        return {
            id: id,
            name: name,
            type: 'province',
            explored: false,
            cities: this.generateCities(id)
        };
    }

    // 生成城市
    generateCities(provinceId) {
        const cities = {};
        const cityCount = 3 + Math.floor(Math.random() * 5); // 3-7个城市

        for (let i = 0; i < cityCount; i++) {
            const cityId = `${provinceId}_city_${i}`;
            cities[cityId] = {
                id: cityId,
                name: null, // 未探索时为null
                type: 'city',
                explored: false,
                counties: this.generateCounties(cityId)
            };
        }

        return cities;
    }

    // 生成县
    generateCounties(cityId) {
        const counties = {};
        const countyCount = 2 + Math.floor(Math.random() * 4); // 2-5个县

        for (let i = 0; i < countyCount; i++) {
            const countyId = `${cityId}_county_${i}`;
            counties[countyId] = {
                id: countyId,
                name: null,
                type: 'county',
                explored: false,
                districts: this.generateDistricts(countyId)
            };
        }

        return counties;
    }

    // 生成区
    generateDistricts(countyId) {
        const districts = {};
        const districtCount = 2 + Math.floor(Math.random() * 3); // 2-4个区

        for (let i = 0; i < districtCount; i++) {
            const districtId = `${countyId}_district_${i}`;
            districts[districtId] = {
                id: districtId,
                name: null,
                type: 'district',
                explored: false,
                streets: this.generateStreets(districtId)
            };
        }

        return districts;
    }

    // 生成街道
    generateStreets(districtId) {
        const streets = {};
        const streetCount = 3 + Math.floor(Math.random() * 5); // 3-7条街道

        for (let i = 0; i < streetCount; i++) {
            const streetId = `${districtId}_street_${i}`;
            streets[streetId] = {
                id: streetId,
                name: null,
                type: 'street',
                explored: false,
                exits: this.generateStreetExits(),
                interestPoints: this.generateInterestPoints(streetId)
            };
        }

        return streets;
    }

    // 生成街道出口
    generateStreetExits() {
        const exits = {};
        const directions = ['north', 'south', 'east', 'west'];
        const exitCount = 1 + Math.floor(Math.random() * 3); // 1-3个出口

        for (let i = 0; i < exitCount; i++) {
            const direction = directions[Math.floor(Math.random() * directions.length)];
            if (!exits[direction]) {
                exits[direction] = {
                    direction: direction,
                    name: this.getDirectionName(direction) + '出口',
                    connected: false, // 是否连接到其他街道
                    targetStreet: null // 连接的目标街道ID
                };
            }
        }

        return exits;
    }

    // 生成兴趣点
    generateInterestPoints(streetId) {
        const points = {};
        const pointCount = 2 + Math.floor(Math.random() * 4); // 2-5个兴趣点

        const pointTypes = [
            '废弃加油站', '废弃超市', '汽修厂', '居民区', '医院',
            '图书馆', '农场', '废料场', '小型工厂', '学校'
        ];

        for (let i = 0; i < pointCount; i++) {
            const pointId = `${streetId}_point_${i}`;
            const pointType = pointTypes[Math.floor(Math.random() * pointTypes.length)];

            points[pointId] = {
                id: pointId,
                name: pointType,
                type: 'interest_point',
                explored: false,
                danger: 0.1 + Math.random() * 0.6, // 0.1-0.7的危险度
                resources: this.generatePointResources(pointType)
            };
        }

        return points;
    }

    // 根据兴趣点类型生成资源
    generatePointResources(pointType) {
        const resourceMap = {
            '废弃加油站': { fuel: [5, 15], scrap: [1, 4] },
            '废弃超市': { food: [3, 8], water: [2, 6], medicine: [0, 2] },
            '汽修厂': { scrap: [5, 12], fuel: [2, 8], tools: [0, 1] },
            '居民区': { food: [1, 5], water: [1, 4], medicine: [0, 3], scrap: [0, 3] },
            '医院': { medicine: [3, 10], electronics: [0, 2], chemicals: [1, 3] },
            '图书馆': { books: [1, 3], medicine: [0, 1], water: [0, 2] },
            '农场': { food: [2, 8], seeds: [1, 4], water: [1, 5] },
            '废料场': { scrap: [10, 25], electronics: [0, 2], rare_metals: [0, 2] },
            '小型工厂': { scrap: [3, 8], electronics: [1, 3], chemicals: [0, 2] },
            '学校': { books: [2, 5], electronics: [0, 1], medicine: [0, 1] }
        };

        return resourceMap[pointType] || { scrap: [1, 3] };
    }

    // 获取方向名称
    getDirectionName(direction) {
        const directionNames = {
            'north': '北',
            'south': '南',
            'east': '东',
            'west': '西'
        };
        return directionNames[direction] || direction;
    }

    // 生成随机名称
    generateRandomName(type) {
        const prefixes = {
            street: ['废墟', '破败', '荒芜', '寂静', '阴暗', '残破', '幽暗', '凄凉'],
            district: ['暗影', '钢铁', '废土', '辐射', '毒雾', '烈焰', '冰霜', '雷鸣'],
            county: ['黑石', '赤土', '灰烬', '血岩', '毒沼', '钢城', '废铁', '骨山'],
            city: ['末日', '废土', '钢铁', '暗黑', '血色', '毒雾', '雷暴', '冰封']
        };

        const suffixes = {
            street: ['街', '巷', '路', '道', '弄'],
            district: ['区', '镇', '坊', '城', '地'],
            county: ['县', '郡', '域', '境', '土'],
            city: ['市', '城', '都', '镇', '堡']
        };

        if (!prefixes[type] || !suffixes[type]) {
            return '未知' + (suffixes[type] ? suffixes[type][0] : '地区');
        }

        const prefix = prefixes[type][Math.floor(Math.random() * prefixes[type].length)];
        const suffix = suffixes[type][Math.floor(Math.random() * suffixes[type].length)];

        return prefix + suffix;
    }

    // 探索并命名区域
    exploreAndNameRegion(regionData, type) {
        if (!regionData.explored) {
            regionData.explored = true;
            if (!regionData.name) {
                regionData.name = this.generateRandomName(type);
            }
        }
        return regionData;
    }

    // 获取当前位置信息
    getCurrentLocation() {
        return this.gameState.map.currentLocation;
    }

    // 设置当前位置
    setCurrentLocation(location) {
        this.gameState.map.currentLocation = location;
    }

    // 根据路径获取区域
    getRegionByPath(path) {
        let current = this.worldMap;

        for (const pathElement of path) {
            if (pathElement.type === 'province' && current.provinces) {
                current = current.provinces[pathElement.id];
            } else if (pathElement.type === 'city' && current.cities) {
                current = current.cities[pathElement.id];
            } else if (pathElement.type === 'county' && current.counties) {
                current = current.counties[pathElement.id];
            } else if (pathElement.type === 'district' && current.districts) {
                current = current.districts[pathElement.id];
            } else if (pathElement.type === 'street' && current.streets) {
                current = current.streets[pathElement.id];
            } else if (pathElement.type === 'interest_point' && current.interestPoints) {
                current = current.interestPoints[pathElement.id];
            } else {
                return null;
            }
        }

        return current;
    }

    // 层级切换 - 向下深入
    drillDown(targetId, targetType) {
        const currentPath = this.gameState.map.currentPath || [];

        // 根据当前层级和目标类型确定新路径
        let newPath = [...currentPath];
        newPath.push({ id: targetId, type: targetType });

        // 获取目标区域
        const targetRegion = this.getRegionByPath(newPath);
        if (!targetRegion) {
            return { success: false, message: '无法找到目标区域' };
        }

        // 探索并命名区域
        this.exploreAndNameRegion(targetRegion, targetType);

        // 更新游戏状态
        this.gameState.map.currentPath = newPath;
        this.gameState.map.currentViewLevel = targetType;

        return {
            success: true,
            message: `进入了${targetRegion.name || '未知区域'}`,
            region: targetRegion
        };
    }

    // 层级切换 - 向上返回
    goUp() {
        const currentPath = this.gameState.map.currentPath || [];

        if (currentPath.length === 0) {
            return { success: false, message: '已经在最高层级' };
        }

        // 移除最后一个路径元素
        const newPath = currentPath.slice(0, -1);

        // 确定新的视图层级
        let newViewLevel = 'country';
        if (newPath.length > 0) {
            newViewLevel = newPath[newPath.length - 1].type;
        }

        // 获取新的当前区域
        const currentRegion = this.getRegionByPath(newPath);

        // 更新游戏状态
        this.gameState.map.currentPath = newPath;
        this.gameState.map.currentViewLevel = newViewLevel;

        return {
            success: true,
            message: `返回到${currentRegion?.name || '废土联邦'}`,
            region: currentRegion || this.worldMap
        };
    }

    // 移动到兴趣点
    moveToInterestPoint(pointId, addLogEntry, updateUI, updateMapDisplay) {
        const currentPath = this.gameState.map.currentPath || [];

        // 构建到兴趣点的完整路径
        const pointPath = [...currentPath, { id: pointId, type: 'interest_point' }];
        const interestPoint = this.getRegionByPath(pointPath);

        if (!interestPoint) {
            addLogEntry('无法找到该兴趣点！');
            return false;
        }

        // 计算移动成本
        const fuelCost = this.calculateMovementCost();

        if (this.gameState.player.fuel < fuelCost) {
            addLogEntry(`燃料不足！需要${fuelCost}单位燃料。`);
            return false;
        }

        // 播放移动音效
        this.soundManager.play('move');

        // 消耗燃料
        this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - fuelCost);

        // 探索兴趣点
        this.exploreAndNameRegion(interestPoint, 'interest_point');

        // 更新当前位置
        this.gameState.map.currentLocation = {
            path: pointPath,
            pointId: pointId,
            pointName: interestPoint.name
        };

        // 设置当前地点信息
        this.gameState.location = {
            name: interestPoint.name,
            description: this.generateLocationDescription(interestPoint),
            explored: false,
            danger: interestPoint.danger,
            type: 'interest_point',
            radiation: false
        };

        addLogEntry(`到达了${interestPoint.name}`);

        updateUI();
        updateMapDisplay();
        return true;
    }

    // 通过街道出口移动
    moveToStreetExit(direction, addLogEntry, updateUI, updateMapDisplay) {
        const currentPath = this.gameState.map.currentPath || [];

        // 确保当前在街道层级
        if (currentPath.length === 0 || currentPath[currentPath.length - 1].type !== 'street') {
            addLogEntry('只能在街道中使用出口移动！');
            return false;
        }

        const currentStreet = this.getRegionByPath(currentPath);
        if (!currentStreet || !currentStreet.exits || !currentStreet.exits[direction]) {
            addLogEntry(`该街道没有${this.getDirectionName(direction)}出口！`);
            return false;
        }

        const exit = currentStreet.exits[direction];

        // 计算移动成本
        const fuelCost = this.calculateMovementCost() * 2; // 街道间移动成本更高

        if (this.gameState.player.fuel < fuelCost) {
            addLogEntry(`燃料不足！需要${fuelCost}单位燃料。`);
            return false;
        }

        // 播放移动音效
        this.soundManager.play('move');

        // 消耗燃料
        this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - fuelCost);

        // 如果出口连接到其他街道，移动到那里
        if (exit.connected && exit.targetStreet) {
            // 这里可以实现街道间的连接逻辑
            addLogEntry(`通过${exit.name}到达了另一条街道`);
        } else {
            // 随机生成新的街道连接
            addLogEntry(`通过${exit.name}离开了当前街道，进入了未知区域`);
        }

        updateUI();
        updateMapDisplay();
        return true;
    }

    // 计算移动成本
    calculateMovementCost() {
        const baseCost = 5;

        // 根据当前层级调整成本
        const levelMultiplier = {
            'interest_point': 1,
            'street': 2,
            'district': 3,
            'county': 5,
            'city': 8,
            'province': 12
        };

        const currentLevel = this.gameState.map.currentViewLevel || 'street';
        return baseCost * (levelMultiplier[currentLevel] || 1);
    }

    // 生成地点描述
    generateLocationDescription(interestPoint) {
        const descriptions = {
            '废弃加油站': '一个破败的加油站，可能还有一些有用的物资...',
            '废弃超市': '曾经繁华的超市，现在只剩下空荡荡的货架...',
            '汽修厂': '满是工具和零件的汽修厂，对改装房车很有用...',
            '居民区': '废弃的住宅区，家家户户都空无一人...',
            '医院': '废弃的医院，医疗用品可能还有剩余...',
            '图书馆': '安静的图书馆，书籍可能包含有用的知识...',
            '农场': '废弃的农场，可能还有一些种子和农产品...',
            '废料场': '巨大的废料堆积场，虽然脏乱但有很多有用的材料...',
            '小型工厂': '一个小型制造工厂，机器已经停止运转...',
            '学校': '空荡荡的学校，教室里还残留着过去的痕迹...'
        };

        return descriptions[interestPoint.name] || '一个神秘的地方，等待着你的探索...';
    }

    // 获取当前可见的区域列表
    getCurrentViewData() {
        const currentPath = this.gameState.map.currentPath || [];
        const currentRegion = this.getRegionByPath(currentPath) || this.worldMap;
        const viewLevel = this.gameState.map.currentViewLevel || 'country';

        let viewData = {
            currentRegion: currentRegion,
            viewLevel: viewLevel,
            items: [],
            canGoUp: currentPath.length > 0,
            pathInfo: this.getPathInfo(currentPath)
        };

        // 根据当前层级获取子项目
        if (viewLevel === 'country' && currentRegion.provinces) {
            viewData.items = Object.values(currentRegion.provinces).map(province => ({
                id: province.id,
                name: province.explored ? province.name : '未知省份',
                type: 'province',
                explored: province.explored
            }));
        } else if (viewLevel === 'province' && currentRegion.cities) {
            viewData.items = Object.values(currentRegion.cities).map(city => ({
                id: city.id,
                name: city.explored ? city.name : '未知城市',
                type: 'city',
                explored: city.explored
            }));
        } else if (viewLevel === 'city' && currentRegion.counties) {
            viewData.items = Object.values(currentRegion.counties).map(county => ({
                id: county.id,
                name: county.explored ? county.name : '未知县城',
                type: 'county',
                explored: county.explored
            }));
        } else if (viewLevel === 'county' && currentRegion.districts) {
            viewData.items = Object.values(currentRegion.districts).map(district => ({
                id: district.id,
                name: district.explored ? district.name : '未知区域',
                type: 'district',
                explored: district.explored
            }));
        } else if (viewLevel === 'district' && currentRegion.streets) {
            viewData.items = Object.values(currentRegion.streets).map(street => ({
                id: street.id,
                name: street.explored ? street.name : '未知街道',
                type: 'street',
                explored: street.explored
            }));
        } else if (viewLevel === 'street') {
            // 街道层级显示兴趣点和出口
            viewData.items = [];

            // 添加兴趣点
            if (currentRegion.interestPoints) {
                viewData.items.push(...Object.values(currentRegion.interestPoints).map(point => ({
                    id: point.id,
                    name: point.name,
                    type: 'interest_point',
                    explored: point.explored,
                    danger: point.danger
                })));
            }

            // 添加出口
            if (currentRegion.exits) {
                viewData.items.push(...Object.values(currentRegion.exits).map(exit => ({
                    id: exit.direction,
                    name: exit.name,
                    type: 'exit',
                    direction: exit.direction,
                    connected: exit.connected
                })));
            }
        }

        return viewData;
    }

    // 获取路径信息
    getPathInfo(path) {
        if (path.length === 0) {
            return '废土联邦';
        }

        let pathString = '废土联邦';
        let currentRegion = this.worldMap;

        for (const pathElement of path) {
            if (pathElement.type === 'province' && currentRegion.provinces) {
                currentRegion = currentRegion.provinces[pathElement.id];
                pathString += ' > ' + (currentRegion.explored ? currentRegion.name : '未知省份');
            } else if (pathElement.type === 'city' && currentRegion.cities) {
                currentRegion = currentRegion.cities[pathElement.id];
                pathString += ' > ' + (currentRegion.explored ? currentRegion.name : '未知城市');
            } else if (pathElement.type === 'county' && currentRegion.counties) {
                currentRegion = currentRegion.counties[pathElement.id];
                pathString += ' > ' + (currentRegion.explored ? currentRegion.name : '未知县城');
            } else if (pathElement.type === 'district' && currentRegion.districts) {
                currentRegion = currentRegion.districts[pathElement.id];
                pathString += ' > ' + (currentRegion.explored ? currentRegion.name : '未知区域');
            } else if (pathElement.type === 'street' && currentRegion.streets) {
                currentRegion = currentRegion.streets[pathElement.id];
                pathString += ' > ' + (currentRegion.explored ? currentRegion.name : '未知街道');
            }
        }

        return pathString;
    }

    // 初始化地图系统
    initializeMap(locations) {
        // 设置初始位置 - 从第一个省份的第一个城市的第一个县的第一个区的第一个街道开始
        const firstProvince = Object.values(this.worldMap.provinces)[0];
        const firstCity = Object.values(firstProvince.cities)[0];
        const firstCounty = Object.values(firstCity.counties)[0];
        const firstDistrict = Object.values(firstCounty.districts)[0];
        const firstStreet = Object.values(firstDistrict.streets)[0];

        // 探索初始路径上的所有区域
        this.exploreAndNameRegion(firstProvince, 'province');
        this.exploreAndNameRegion(firstCity, 'city');
        this.exploreAndNameRegion(firstCounty, 'county');
        this.exploreAndNameRegion(firstDistrict, 'district');
        this.exploreAndNameRegion(firstStreet, 'street');

        // 设置初始路径
        const initialPath = [
            { id: firstProvince.id, type: 'province' },
            { id: firstCity.id, type: 'city' },
            { id: firstCounty.id, type: 'county' },
            { id: firstDistrict.id, type: 'district' },
            { id: firstStreet.id, type: 'street' }
        ];

        // 初始化游戏状态
        this.gameState.map.currentPath = initialPath;
        this.gameState.map.currentViewLevel = 'street';

        // 选择第一个兴趣点作为起始位置
        const firstInterestPoint = Object.values(firstStreet.interestPoints)[0];
        if (firstInterestPoint) {
            this.gameState.map.currentLocation = {
                path: [...initialPath, { id: firstInterestPoint.id, type: 'interest_point' }],
                pointId: firstInterestPoint.id,
                pointName: firstInterestPoint.name
            };

            // 设置当前地点信息
            this.gameState.location = {
                name: firstInterestPoint.name,
                description: this.generateLocationDescription(firstInterestPoint),
                explored: false,
                danger: firstInterestPoint.danger,
                type: 'interest_point',
                radiation: false
            };
        }
    }

    // 兼容旧系统的方法
    getCurrentMapData() {
        return {
            name: this.getPathInfo(this.gameState.map.currentPath || []),
            size: 5, // 固定大小，用于兼容
            type: this.gameState.map.currentViewLevel || 'street'
        };
    }

    getLocationKey(x, y) {
        return `${x},${y}`;
    }

    isLocationKnown(x, y) {
        // 在新系统中，所有位置都是"已知"的，但可能未探索
        return true;
    }

    isLocationExplored(x, y) {
        // 简化的探索检查
        return false;
    }

    revealNearbyLocations(x, y) {
        // 在新系统中不需要这个功能
    }

    // 兼容旧的移动方法
    moveToLocation(x, y, locations, addLogEntry, updateUI, updateMapDisplay) {
        addLogEntry("请使用新的地图系统进行移动！");
        return false;
    }

    // 兼容旧的区域切换方法
    changeRegion(direction, addLogEntry, updateUI, updateMapDisplay) {
        if (direction === 'up') {
            const result = this.goUp();
            addLogEntry(result.message);
            if (result.success) {
                updateUI();
                updateMapDisplay();
            }
        } else {
            addLogEntry("请使用新的地图界面进行导航！");
        }
    }

    getLocationIcon(type) {
        const icons = {
            'interest_point': '📍',
            'exit': '🚪',
            'province': '🏛️',
            'city': '🌆',
            'county': '🏘️',
            'district': '🏢',
            'street': '🛣️'
        };
        return icons[type] || '❓';
    }

}
