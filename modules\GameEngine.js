// 主游戏引擎模块
import { SoundManager } from './SoundManager.js';
import { GameData } from './GameData.js';
import { MapSystem } from './MapSystem.js';
import { InventorySystem } from './InventorySystem.js';
import { CombatSystem } from './CombatSystem.js';
import { UpgradeSystem } from './UpgradeSystem.js';
import { UIManager } from './UIManager.js';
import { MemorySystem } from './MemorySystem.js';
import { TimeSystem } from './TimeSystem.js';
import { WeightSystem } from './WeightSystem.js';
import { EncounterSystem } from './EncounterSystem.js';
import { CraftingSystem } from './CraftingSystem.js';

export class GameEngine {
    constructor() {
        // 初始化游戏状态
        this.gameState = GameData.getInitialGameState();

        // 初始化各个系统
        this.soundManager = new SoundManager();
        this.timeSystem = new TimeSystem(this.gameState);
        this.memorySystem = new MemorySystem(this.gameState, this.soundManager);
        this.weightSystem = new WeightSystem(this.gameState, this.soundManager);
        this.uiManager = new UIManager(this.gameState);
        this.inventorySystem = new InventorySystem(this.gameState, this.soundManager);
        this.mapSystem = new MapSystem(this.gameState, this.soundManager);
        this.combatSystem = new CombatSystem(this.gameState, this.soundManager, this.inventorySystem);
        this.upgradeSystem = new UpgradeSystem(this.gameState, this.soundManager, this.inventorySystem);
        this.encounterSystem = new EncounterSystem(this.gameState, this.soundManager, this.timeSystem);
        this.craftingSystem = new CraftingSystem(this.gameState, this.soundManager, this.inventorySystem);

        // 获取游戏数据
        this.locations = GameData.getLocations();

        this.init();
    }

    init() {
        // 初始化地图系统
        this.mapSystem.initializeMap(this.locations);

        // 启动时间系统
        this.timeSystem.startTimeFlow();

        this.updateUI();
        this.updateInventory();
        this.addLogEntry("游戏开始！你驾驶着房车在废土上开始了生存之旅...");

        // 自动保存
        setInterval(() => this.saveGame(), 30000);

        // 时间流逝和系统更新
        setInterval(() => this.timePass(), 10000);

        // 定期更新负重显示
        setInterval(() => this.weightSystem.updateWeightDisplay(), 5000);
    }

    // 委托方法到各个系统
    updateUI() {
        this.uiManager.updateUI();
    }

    updateInventory() {
        this.inventorySystem.updateInventoryDisplay();
    }

    addLogEntry(message) {
        this.uiManager.addLogEntry(message);
    }

    updateMapDisplay() {
        this.uiManager.updateMapDisplay(this.mapSystem);
    }

    // 探索相关方法
    exploreLocation() {
        this.soundManager.play('explore');

        // 检查遭遇
        const encounter = this.encounterSystem.checkEncounter('exploration');
        if (encounter) {
            this.showEncounter(encounter);
            return;
        }

        // 推进时间
        this.timeSystem.advanceTime(this.timeSystem.calculateActivityTime(15, 'explore'));

        if (this.gameState.location.explored) {
            this.addLogEntry("这个地方已经被搜索过了，没有发现新的物资。");
            return;
        }

        // 检查是否遭遇敌人
        if (Math.random() < this.gameState.location.danger) {
            this.combatSystem.startCombat(
                this.addLogEntry.bind(this),
                this.combatSystem.addCombatLog.bind(this.combatSystem),
                this.updateUI.bind(this),
                this.combatSystem.updateCombatUI.bind(this.combatSystem)
            );
            return;
        }

        // 搜索物资
        const location = this.locations.find(loc => loc.name === this.gameState.location.name);
        if (location) {
            const foundItems = this.inventorySystem.generateLoot(location.loot);

            if (foundItems.length > 0) {
                this.soundManager.play('loot');
                this.addLogEntry(`搜索成功！发现了：${foundItems.join(', ')}`);
            } else {
                this.addLogEntry("搜索了一番，但没有发现有用的物资。");
            }

            // 记录到回忆系统
            const locationKey = `${this.gameState.map.currentLocation.x},${this.gameState.map.currentLocation.y}`;
            this.memorySystem.recordVisit(
                locationKey,
                this.gameState.location,
                foundItems,
                this.gameState.location.danger
            );

            this.gameState.location.explored = true;
            this.updateInventory();
        }

        // 消耗饥饿度
        this.gameState.player.hunger = Math.max(0, this.gameState.player.hunger - 5);
        this.updateUI();
    }

    moveToNextLocation() {
        // 保留原有的随机移动功能作为"随机探索"
        const mapData = this.mapSystem.getCurrentMapData();
        const currentLoc = this.gameState.map.currentLocation;

        // 在当前位置周围寻找未探索的地点
        const nearbyLocations = [];
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                if (dx === 0 && dy === 0) continue;
                const newX = currentLoc.x + dx;
                const newY = currentLoc.y + dy;
                if (newX >= 0 && newX < mapData.size && newY >= 0 && newY < mapData.size) {
                    nearbyLocations.push({ x: newX, y: newY });
                }
            }
        }

        if (nearbyLocations.length > 0) {
            const randomLocation = nearbyLocations[Math.floor(Math.random() * nearbyLocations.length)];
            this.moveToMapLocation(randomLocation.x, randomLocation.y);
        } else {
            this.addLogEntry("周围没有可以探索的地点了！");
        }
    }

    moveToMapLocation(x, y) {
        // 检查遭遇
        const encounter = this.encounterSystem.checkEncounter('travel');
        if (encounter) {
            this.showEncounter(encounter);
            return false;
        }

        // 计算移动时间和燃料消耗（考虑负重）
        const baseTime = 10;
        const baseFuel = 5;

        const timeModifier = this.weightSystem.getTimeModifier();
        const fuelModifier = this.weightSystem.getFuelModifier();

        const actualTime = Math.ceil(baseTime * timeModifier);
        const actualFuel = Math.ceil(baseFuel * fuelModifier);

        // 检查燃料是否足够
        if (this.gameState.player.fuel < actualFuel) {
            this.addLogEntry(`燃料不足！需要${actualFuel}单位燃料（负重影响+${Math.floor((fuelModifier - 1) * 100)}%）`);
            return false;
        }

        // 推进时间
        this.timeSystem.advanceTime(actualTime);

        // 消耗燃料
        this.gameState.player.fuel -= actualFuel;

        // 显示负重影响
        if (this.gameState.overweight) {
            const warning = this.weightSystem.getOverweightWarning();
            this.addLogEntry(`超重影响：移动时间+${Math.floor((timeModifier - 1) * 100)}%，燃料消耗+${Math.floor((fuelModifier - 1) * 100)}%`);
        }

        return this.mapSystem.moveToLocation(
            x, y, this.locations,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateMapDisplay.bind(this)
        );
    }

    restAtLocation() {
        this.inventorySystem.restAtLocation(
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this)
        );
    }

    useItem(item) {
        this.inventorySystem.useItem(
            item,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this)
        );
    }

    // 地图相关方法
    openMapPanel() {
        this.uiManager.openMapPanel(this.mapSystem);
    }

    closeMapPanel() {
        this.uiManager.closeMapPanel();
    }

    // 新地图系统方法
    drillDownToRegion(regionId, regionType) {
        const result = this.mapSystem.drillDown(regionId, regionType);
        this.addLogEntry(result.message);
        if (result.success) {
            this.updateUI();
            this.updateMapDisplay();
        }
    }

    goUpInMap() {
        const result = this.mapSystem.goUp();
        this.addLogEntry(result.message);
        if (result.success) {
            this.updateUI();
            this.updateMapDisplay();
        }
    }

    moveToInterestPoint(pointId) {
        return this.mapSystem.moveToInterestPoint(
            pointId,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateMapDisplay.bind(this)
        );
    }

    moveToStreetExit(direction) {
        return this.mapSystem.moveToStreetExit(
            direction,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateMapDisplay.bind(this)
        );
    }

    // 兼容旧系统的方法
    changeRegionUp() {
        this.goUpInMap();
    }

    changeRegionDown() {
        this.addLogEntry("请使用新的地图界面进行导航！");
    }

    // 升级相关方法
    openUpgradePanel() {
        this.upgradeSystem.openUpgradePanel();
    }

    closeUpgradePanel() {
        this.upgradeSystem.closeUpgradePanel();
    }

    purchaseUpgrade(category, level, cost) {
        this.upgradeSystem.purchaseUpgrade(
            category, level, cost,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this)
        );
    }

    // 战斗相关方法
    attackEnemy() {
        this.combatSystem.attackEnemy(
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this)
        );
    }

    defendAction() {
        this.combatSystem.defendAction(
            this.addLogEntry.bind(this),
            this.updateUI.bind(this)
        );
    }

    fleeFromCombat() {
        this.combatSystem.fleeFromCombat(
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this)
        );
    }

    // 音效控制
    toggleSound() {
        return this.uiManager.toggleSound(this.soundManager);
    }

    // 制作系统方法
    openCraftingPanel() {
        this.showCraftingPanel();
    }

    closeCraftingPanel() {
        this.hideCraftingPanel();
    }

    showCraftingPanel() {
        const craftingScreen = document.getElementById('crafting-screen');
        const gameScreen = document.getElementById('game-screen');

        if (craftingScreen && gameScreen) {
            gameScreen.classList.remove('active');
            craftingScreen.classList.add('active');
            this.updateCraftingDisplay();
        }
    }

    hideCraftingPanel() {
        const craftingScreen = document.getElementById('crafting-screen');
        const gameScreen = document.getElementById('game-screen');

        if (craftingScreen && gameScreen) {
            craftingScreen.classList.remove('active');
            gameScreen.classList.add('active');
        }
    }

    updateCraftingDisplay() {
        const recipes = this.craftingSystem.getAllRecipes();

        // 按类别分组
        const categories = {
            armor: { name: '防具', items: [] },
            weapons: { name: '武器', items: [] },
            tools: { name: '工具', items: [] },
            consumables: { name: '消耗品', items: [] },
            accessories: { name: '辅助装备', items: [] }
        };

        recipes.forEach(recipe => {
            if (categories[recipe.category]) {
                categories[recipe.category].items.push(recipe);
            }
        });

        // 更新界面
        const craftingCategories = document.getElementById('crafting-categories');
        if (craftingCategories) {
            craftingCategories.innerHTML = '';

            for (const [categoryId, category] of Object.entries(categories)) {
                if (category.items.length > 0) {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'crafting-category';
                    categoryDiv.innerHTML = `
                        <h3>${category.name}</h3>
                        <div class="crafting-items" id="${categoryId}-items">
                            ${category.items.map(item => this.createCraftingItemHTML(item)).join('')}
                        </div>
                    `;
                    craftingCategories.appendChild(categoryDiv);
                }
            }
        }

        // 更新装备显示
        this.updateEquipmentDisplay();
    }

    createCraftingItemHTML(recipe) {
        const canCraft = recipe.canCraft.canCraft;
        const materials = Object.entries(recipe.materials)
            .map(([mat, qty]) => {
                const available = this.gameState.inventory[mat] || 0;
                const hasEnough = available >= qty;
                return `<span class="${hasEnough ? 'available' : 'missing'}">${this.inventorySystem.getItemName(mat)} ${available}/${qty}</span>`;
            }).join(', ');

        return `
            <div class="crafting-item ${canCraft ? 'craftable' : 'not-craftable'}">
                <div class="item-header">
                    <span class="item-icon">${recipe.icon}</span>
                    <h4>${recipe.name}</h4>
                    <span class="item-level">Lv.${recipe.level}</span>
                </div>
                <p class="item-description">${recipe.description}</p>
                <div class="item-materials">
                    <strong>材料:</strong> ${materials}
                </div>
                <div class="item-time">制作时间: ${recipe.time}分钟</div>
                <div class="item-stats">
                    ${Object.entries(recipe.stats).map(([stat, value]) =>
                        `<span class="stat">${stat}: ${value > 0 ? '+' : ''}${value}</span>`
                    ).join(' ')}
                </div>
                <button class="craft-btn"
                        ${canCraft ? '' : 'disabled'}
                        onclick="craftItem('${recipe.category}', '${recipe.itemId}')">
                    ${canCraft ? '制作' : recipe.canCraft.reason}
                </button>
            </div>
        `;
    }

    craftItem(category, itemId) {
        const success = this.craftingSystem.craftItem(
            category,
            itemId,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this),
            this.updateInventory.bind(this),
            this.timeSystem
        );

        if (success) {
            this.updateCraftingDisplay();
        }
    }

    updateEquipmentDisplay() {
        const equipment = this.gameState.equipment;

        // 更新装备槽显示
        ['armor', 'weapon', 'tool', 'accessory'].forEach(slot => {
            const slotElement = document.getElementById(`equipped-${slot}`);
            if (slotElement) {
                const item = equipment[slot];
                if (item) {
                    slotElement.innerHTML = `
                        <div class="equipped-item">
                            <span class="item-icon">${item.icon}</span>
                            <span class="item-name">${item.name}</span>
                            <button onclick="unequipItem('${slot}')" class="unequip-btn">卸下</button>
                        </div>
                    `;
                } else {
                    slotElement.innerHTML = `<div class="empty-slot">空</div>`;
                }
            }
        });

        // 更新装备加成显示
        const bonuses = this.craftingSystem.getEquipmentBonuses();
        const bonusElement = document.getElementById('equipment-bonuses');
        if (bonusElement) {
            const bonusText = Object.entries(bonuses)
                .filter(([stat, value]) => value !== 0)
                .map(([stat, value]) => `${stat}: ${value > 0 ? '+' : ''}${value}`)
                .join(', ');
            bonusElement.textContent = bonusText || '无加成';
        }
    }

    unequipItem(slot) {
        if (this.gameState.equipment[slot]) {
            this.addLogEntry(`卸下了${this.gameState.equipment[slot].name}`);
            this.gameState.equipment[slot] = null;
            this.updateEquipmentDisplay();
            this.updateUI();
        }
    }



    useConsumable(itemId) {
        this.craftingSystem.useConsumable(
            itemId,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this)
        );
        this.updateInventory();
    }

    // 回忆系统方法
    openMemoryPanel() {
        this.showMemoryPanel();
    }

    closeMemoryPanel() {
        this.hideMemoryPanel();
    }

    showMemoryPanel() {
        const memoryScreen = document.getElementById('memory-screen');
        const gameScreen = document.getElementById('game-screen');

        if (memoryScreen && gameScreen) {
            gameScreen.classList.remove('active');
            memoryScreen.classList.add('active');
            this.updateMemoryDisplay();
        }
    }

    hideMemoryPanel() {
        const memoryScreen = document.getElementById('memory-screen');
        const gameScreen = document.getElementById('game-screen');

        if (memoryScreen && gameScreen) {
            memoryScreen.classList.remove('active');
            gameScreen.classList.add('active');
        }
    }

    updateMemoryDisplay() {
        const visitedLocations = this.memorySystem.getAllVisitedLocations();

        // 更新统计信息
        const exploredCount = document.getElementById('explored-count');
        const totalVisits = document.getElementById('total-visits');
        const totalResourcesFound = document.getElementById('total-resources-found');

        if (exploredCount) exploredCount.textContent = visitedLocations.length;
        if (totalVisits) {
            const visits = visitedLocations.reduce((sum, loc) => sum + loc.data.visitCount, 0);
            totalVisits.textContent = visits;
        }
        if (totalResourcesFound) {
            const resources = visitedLocations.reduce((sum, loc) => sum + loc.data.totalResourcesGained, 0);
            totalResourcesFound.textContent = resources;
        }

        // 更新地点列表
        const memoryList = document.getElementById('memory-list');
        if (memoryList) {
            memoryList.innerHTML = '';

            visitedLocations.forEach(location => {
                const memoryItem = document.createElement('div');
                memoryItem.className = 'memory-item';
                memoryItem.innerHTML = `
                    <h4>${location.data.name}</h4>
                    <div class="memory-details">
                        <div>访问次数: ${location.data.visitCount}</div>
                        <div>燃料折扣: ${Math.floor(location.discount * 100)}%</div>
                        <div>发现资源: ${location.data.totalResourcesGained}</div>
                        <div>危险等级: ${location.rewards?.dangerLevel?.toFixed(1) || '未知'}</div>
                    </div>
                    ${location.note ? `<div class="memory-note">${location.note.note}</div>` : ''}
                `;

                memoryItem.onclick = () => {
                    if (confirm(`是否快速返回到${location.data.name}？`)) {
                        this.memorySystem.fastTravelTo(
                            location.key,
                            this.mapSystem,
                            this.addLogEntry.bind(this),
                            this.updateUI.bind(this),
                            this.updateMapDisplay.bind(this)
                        );
                        this.hideMemoryPanel();
                    }
                };

                memoryList.appendChild(memoryItem);
            });
        }
    }

    // 遭遇系统方法
    showEncounter(encounter) {
        const encounterScreen = document.getElementById('encounter-screen');
        const gameScreen = document.getElementById('game-screen');

        if (encounterScreen && gameScreen) {
            gameScreen.classList.remove('active');
            encounterScreen.classList.add('active');

            // 更新遭遇内容
            const title = document.getElementById('encounter-title');
            const description = document.getElementById('encounter-description');
            const options = document.getElementById('encounter-options');

            if (title) title.textContent = `⚠️ ${encounter.name}`;
            if (description) {
                description.textContent = this.encounterSystem.generateEncounterDescription(encounter);
            }

            if (options) {
                options.innerHTML = '';
                encounter.options.forEach((option, index) => {
                    const optionBtn = document.createElement('button');
                    optionBtn.className = 'encounter-option';
                    optionBtn.innerHTML = `
                        <div class="option-text">${option.text}</div>
                        <div class="option-details">${this.getOptionDetails(option)}</div>
                    `;
                    optionBtn.onclick = () => this.handleEncounterChoice(encounter, index);
                    options.appendChild(optionBtn);
                });
            }
        }
    }

    getOptionDetails(option) {
        let details = [];
        if (option.stat) details.push(`需要: ${option.stat}`);
        if (option.difficulty) details.push(`难度: ${option.difficulty}`);
        if (option.cost) details.push(`消耗: ${option.cost}`);
        if (option.time) details.push(`时间: ${option.time}分钟`);
        return details.join(' | ') || '无特殊要求';
    }

    handleEncounterChoice(encounter, choiceIndex) {
        const success = this.encounterSystem.handleEncounterChoice(
            encounter,
            choiceIndex,
            this.addLogEntry.bind(this),
            this.updateUI.bind(this)
        );

        // 关闭遭遇界面
        setTimeout(() => {
            this.hideEncounter();
        }, 2000);
    }

    hideEncounter() {
        const encounterScreen = document.getElementById('encounter-screen');
        const gameScreen = document.getElementById('game-screen');

        if (encounterScreen && gameScreen) {
            encounterScreen.classList.remove('active');
            gameScreen.classList.add('active');
        }
    }

    // 时间系统
    timePass() {
        // 饥饿度自然下降
        this.gameState.player.hunger = Math.max(0, this.gameState.player.hunger - 2);

        // 饥饿时生命值下降
        if (this.gameState.player.hunger < 20) {
            this.gameState.player.health = Math.max(0, this.gameState.player.health - 1);
            if (this.gameState.player.hunger === 0) {
                this.addLogEntry("你感到非常饥饿，生命值在下降！");
            }
        }

        // 检查游戏结束条件
        if (this.gameState.player.health <= 0) {
            this.gameOver();
        }

        // 更新负重状态
        this.weightSystem.checkOverweight();

        this.updateUI();
    }

    triggerRandomEvent() {
        const events = [
            {
                name: "发现补给箱",
                description: "你在路边发现了一个遗落的补给箱！",
                effect: () => {
                    const items = ['food', 'water', 'fuel', 'medicine'];
                    const item = items[Math.floor(Math.random() * items.length)];
                    const amount = Math.floor(Math.random() * 3) + 1;
                    this.inventorySystem.addItem(item, amount);
                    this.addLogEntry(`获得了${this.inventorySystem.getItemName(item)} ×${amount}！`);
                    this.updateInventory();
                }
            },
            {
                name: "引擎故障",
                description: "房车引擎出现了小故障，消耗了一些燃料。",
                effect: () => {
                    this.gameState.player.fuel = Math.max(0, this.gameState.player.fuel - 5);
                    this.addLogEntry("引擎故障导致燃料泄漏！");
                }
            },
            {
                name: "好天气",
                description: "今天天气不错，心情愉悦。",
                effect: () => {
                    this.gameState.player.health = Math.min(this.gameState.player.maxHealth,
                        this.gameState.player.health + 5);
                    this.addLogEntry("好天气让你感觉更好了！");
                }
            },
            {
                name: "找到水源",
                description: "你找到了一个干净的水源！",
                effect: () => {
                    this.inventorySystem.addItem('water', 2);
                    this.addLogEntry("找到了干净的水源，获得了水 ×2！");
                    this.updateInventory();
                }
            }
        ];

        if (Math.random() < 0.3) { // 30%概率触发随机事件
            const event = events[Math.floor(Math.random() * events.length)];
            this.addLogEntry(`随机事件: ${event.description}`);
            event.effect();
        }
    }

    gameOver() {
        alert(`游戏结束！你在废土上生存了${this.gameState.day}天。`);
        if (confirm("是否重新开始游戏？")) {
            location.reload();
        }
    }

    // 存档系统
    saveGame() {
        // 转换Set为数组以便JSON序列化
        const saveData = {
            ...this.gameState,
            map: {
                ...this.gameState.map,
                exploredLocations: Array.from(this.gameState.map.exploredLocations),
                knownLocations: Array.from(this.gameState.map.knownLocations)
            }
        };
        localStorage.setItem('apocalypseRVSave', JSON.stringify(saveData));
    }

    loadGame() {
        const saved = localStorage.getItem('apocalypseRVSave');
        if (saved) {
            const saveData = JSON.parse(saved);
            this.gameState = {
                ...saveData,
                map: {
                    ...saveData.map,
                    exploredLocations: new Set(saveData.map.exploredLocations || []),
                    knownLocations: new Set(saveData.map.knownLocations || [])
                }
            };

            // 重新初始化系统状态
            this.uiManager.gameState = this.gameState;
            this.inventorySystem.gameState = this.gameState;
            this.mapSystem.gameState = this.gameState;
            this.combatSystem.gameState = this.gameState;
            this.upgradeSystem.gameState = this.gameState;

            this.updateUI();
            this.updateInventory();
            this.updateMapDisplay();
        }
    }
}
