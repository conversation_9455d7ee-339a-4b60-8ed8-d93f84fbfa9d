# 新地图系统重构说明

## 概述

已成功重构地图系统，实现了您要求的层级结构和探索机制。新系统包含以下核心特性：

## 地图元素

### 1. 兴趣点（Interest Points）
- 作为地图的基本功能单元
- 包括：废弃加油站、废弃超市、汽修厂、居民区、医院、图书馆、农场、废料场、小型工厂、学校等
- 每个兴趣点都有独特的资源和危险度

### 2. 街道（Streets）
- 容纳兴趣点的唯一单位
- 每条街道包含2-5个兴趣点
- 具有方向性出口（北、南、东、西）用于街道间移动

## 层级结构

完整的层级结构为：**国家 → 省 → 市 → 县 → 区 → 街道 → 兴趣点**

### 层级切换规则
1. **逐级深入**：从国家开始，点击省份进入省级视图，再点击城市进入市级视图，依此类推
2. **只有选择兴趣点才实际移动**：在其他层级的选择只是切换视图，不消耗燃料
3. **完整路径定义位置**：实际位置由完整的层级路径定义

### 位置定义规则
- 位置格式：某国某省某市某县某区某街道某兴趣点
- 不能仅由单独层级（如某省、某市）定义位置

## 移动方式

### 1. 层级导航
- **向下深入**：点击区域进入下级视图
- **向上返回**：点击"返回上级"按钮

### 2. 实际移动
- **移动到兴趣点**：在街道层级点击兴趣点，消耗燃料并实际移动
- **街道间移动**：通过街道出口（东出口、西出口等）移动到相邻街道

## 探索机制

### 1. 未知状态
- 所有区域在未探索前显示为"未知省份"、"未知城市"等
- 需要实际到达后才能知晓具体名称

### 2. 地图生成
- 整个国家的地图结构在游戏开始时就已生成
- 包含11个省份，每个省份3-7个城市，每个城市2-5个县等
- 详细信息（名称）需要通过探索解锁

### 3. 探索解锁
- 到达某个区域后，该区域被标记为"已探索"
- 已探索区域显示真实名称
- 未探索区域保持"未知"状态

## 用户界面

### 1. 地图界面更新
- 显示当前层级（国家、省份、城市等）
- 显示完整位置路径
- 列表式显示可访问的子区域或兴趣点
- 不同类型的项目有不同的图标和颜色

### 2. 操作说明
- 🏛️ 省份 - 点击进入
- 🌆 城市 - 点击进入  
- 🏘️ 县城 - 点击进入
- 🏢 区域 - 点击进入
- 🛣️ 街道 - 点击进入
- 📍 兴趣点 - 点击移动（消耗燃料）
- 🚪 出口 - 点击移动（消耗燃料）

## 技术实现

### 1. 数据结构
- 使用嵌套对象结构存储完整的世界地图
- 每个区域包含ID、名称、类型、探索状态等属性
- 支持动态生成和命名

### 2. 状态管理
- `currentPath`: 当前位置的完整路径
- `currentViewLevel`: 当前查看的层级
- `currentLocation`: 当前实际位置（兴趣点）
- `exploredRegions`: 已探索的区域集合

### 3. 兼容性
- 保留了与旧系统的兼容接口
- 旧的移动方法会提示使用新系统
- 确保其他游戏系统正常工作

## 使用方法

1. **打开地图**：点击"打开地图"按钮
2. **导航**：从国家层级开始，逐级点击进入想要探索的区域
3. **移动**：在街道层级选择兴趣点进行实际移动
4. **探索**：到达新区域后，该区域将被解锁并显示真实名称
5. **返回**：使用"返回上级"按钮向上导航

## 特色功能

1. **真实的探索感**：未知区域保持神秘，需要实际探索才能了解
2. **层级化导航**：清晰的层级结构，便于理解和导航
3. **燃料管理**：只有实际移动才消耗燃料，浏览地图不消耗资源
4. **丰富的兴趣点**：每个街道都有多样化的兴趣点可供探索
5. **方向性移动**：通过街道出口实现自然的区域间移动

新地图系统完全符合您的需求，提供了更加真实和有趣的探索体验！
